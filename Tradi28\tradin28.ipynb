# Test Cell - Run this first to verify output is working
print("Testing notebook output...")
print("If you can see this message, output is working correctly.")
print("Now you can run the training cell below.")

# Cell 1: Setup and Imports
# Install required packages
!pip install ta pandas numpy scikit-learn tensorflow -q

# Import required libraries
import pandas as pd
import numpy as np
from ta.trend import SMAIndicator, EMAIndicator, MACD
from ta.momentum import RSIIndicator, StochRSIIndicator
from ta.volatility import BollingerBands, AverageTrueRange
from sklearn.preprocessing import StandardScaler
import gc  # For garbage collection
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set up project directories
from google.colab import drive
drive.mount('/content/drive')

# Define project directories
PROJECT_DIR = Path('/content/drive/MyDrive/V75s_Trading')
DATA_DIR = PROJECT_DIR / 'historical_data'
SAVE_DIR = PROJECT_DIR / 'preprocessed_data'

# Create directories if they don't exist
PROJECT_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)
SAVE_DIR.mkdir(exist_ok=True)

print(f"Project directory: {PROJECT_DIR}")
print(f"Data directory: {DATA_DIR}")
print(f"Save directory: {SAVE_DIR}")

# Verify data files exist
print("\nChecking data files...")
timeframes = ['1m', '5m', '30m', '1h', '8h']
missing_files = []

for tf in timeframes:
    file_path = DATA_DIR / f'v75s_{tf}_historical.csv'
    if not file_path.exists():
        missing_files.append(f'v75s_{tf}_historical.csv')

if missing_files:
    print("Error: The following data files are missing:")
    for file in missing_files:
        print(f"- {file}")
    print(f"\nPlease ensure all data files are present in: {DATA_DIR}")
    raise FileNotFoundError("Missing data files")

print("All data files found. Starting preprocessing...")

# Cell 2: Feature Engineering Functions

def add_technical_indicators(df):
    """Add essential technical indicators with memory optimization"""
    print("Adding technical indicators...")
    
    # Core moving averages
    df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()
    df['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()
    df['ema_20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
    
    # MACD
    macd = MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()
    
    # RSI and Stochastic
    df['rsi'] = RSIIndicator(close=df['close']).rsi()
    df['stoch_rsi_k'] = StochRSIIndicator(close=df['close']).stochrsi_k()
    
    # Bollinger and ATR
    bb = BollingerBands(close=df['close'])
    df['bb_high'] = bb.bollinger_hband()
    df['bb_low'] = bb.bollinger_lband()
    df['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
    
    gc.collect()
    return df

def calculate_market_regime(df):
    """Simplified market regime analysis"""
    df['volatility'] = (df['high'] - df['low']) / df['close']
    df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['close']
    return df

def calculate_candle_patterns(df):
    """Basic candlestick patterns"""
    df['body'] = df['close'] - df['open']
    df['total_range'] = df['high'] - df['low']
    return df

def create_targets(df, pip_value=0.0001):
    """Create essential target variables"""
    timeframes = {
        'scalp': {'minutes': 5, 'min_pips': 5, 'max_pips': 20},
        'day': {'minutes': 1440, 'min_pips': 20, 'max_pips': 100},
        'swing': {'minutes': 10080, 'min_pips': 100, 'max_pips': 300},
        'position': {'minutes': 10080 * 2, 'min_pips': 300, 'max_pips': 1000}
    }
    
    for style, params in timeframes.items():
        print(f"Creating {style} targets...")
        future_prices = df['close'].shift(-params['minutes'])
        price_diff = (future_prices - df['close']) / pip_value
        
        df[f'{style}_direction'] = np.where(price_diff > 0, 1, 0)
        df[f'{style}_pips'] = abs(price_diff)
        df[f'{style}_valid'] = (abs(price_diff) >= params['min_pips']) & (abs(price_diff) <= params['max_pips'])
        
        rolling_high = df['high'].rolling(window=params['minutes'], min_periods=1).max().shift(-params['minutes'])
        rolling_low = df['low'].rolling(window=params['minutes'], min_periods=1).min().shift(-params['minutes'])
        df[f'{style}_tp'] = np.where(price_diff > 0, rolling_high, rolling_low)
        df[f'{style}_sl'] = np.where(price_diff > 0, rolling_low, rolling_high)
        
        gc.collect()
    
    return df

# Cell 3: Main Processing Loop

# Ensure we have required imports
import gc
import psutil
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from ta.trend import SMAIndicator, EMAIndicator, MACD
from ta.momentum import RSIIndicator, StochRSIIndicator
from ta.volatility import BollingerBands, AverageTrueRange

# Get directories from environment
PROJECT_DIR = Path('/content/drive/MyDrive/V75s_Trading')
DATA_DIR = PROJECT_DIR / 'historical_data'
SAVE_DIR = PROJECT_DIR / 'preprocessed_data'

# Feature Engineering Functions
def add_technical_indicators(df):
    """Add essential technical indicators with memory optimization"""
    print("Adding technical indicators...")
    
    # Core moving averages
    df['sma_20'] = SMAIndicator(close=df['close'], window=20).sma_indicator()
    df['sma_50'] = SMAIndicator(close=df['close'], window=50).sma_indicator()
    df['ema_20'] = EMAIndicator(close=df['close'], window=20).ema_indicator()
    
    # MACD
    macd = MACD(close=df['close'])
    df['macd'] = macd.macd()
    df['macd_signal'] = macd.macd_signal()
    
    # RSI and Stochastic
    df['rsi'] = RSIIndicator(close=df['close']).rsi()
    df['stoch_rsi_k'] = StochRSIIndicator(close=df['close']).stochrsi_k()
    
    # Bollinger and ATR
    bb = BollingerBands(close=df['close'])
    df['bb_high'] = bb.bollinger_hband()
    df['bb_low'] = bb.bollinger_lband()
    df['atr'] = AverageTrueRange(high=df['high'], low=df['low'], close=df['close']).average_true_range()
    
    gc.collect()
    return df

def calculate_market_regime(df):
    """Simplified market regime analysis"""
    df['volatility'] = (df['high'] - df['low']) / df['close']
    df['trend_strength'] = abs(df['close'] - df['sma_20']) / df['close']
    return df

def calculate_candle_patterns(df):
    """Basic candlestick patterns"""
    df['body'] = df['close'] - df['open']
    df['total_range'] = df['high'] - df['low']
    return df

def create_targets(df, pip_value=0.0001):
    """Create essential target variables"""
    timeframes = {
        'scalp': {'minutes': 5, 'min_pips': 5, 'max_pips': 20},
        'day': {'minutes': 1440, 'min_pips': 20, 'max_pips': 100},
        'swing': {'minutes': 10080, 'min_pips': 100, 'max_pips': 300},
        'position': {'minutes': 10080 * 2, 'min_pips': 300, 'max_pips': 1000}
    }
    
    for style, params in timeframes.items():
        print(f"Creating {style} targets...")
        future_prices = df['close'].shift(-params['minutes'])
        price_diff = (future_prices - df['close']) / pip_value
        
        df[f'{style}_direction'] = np.where(price_diff > 0, 1, 0)
        df[f'{style}_pips'] = abs(price_diff)
        df[f'{style}_valid'] = (abs(price_diff) >= params['min_pips']) & (abs(price_diff) <= params['max_pips'])
        
        rolling_high = df['high'].rolling(window=params['minutes'], min_periods=1).max().shift(-params['minutes'])
        rolling_low = df['low'].rolling(window=params['minutes'], min_periods=1).min().shift(-params['minutes'])
        df[f'{style}_tp'] = np.where(price_diff > 0, rolling_high, rolling_low)
        df[f'{style}_sl'] = np.where(price_diff > 0, rolling_low, rolling_high)
        
        gc.collect()
    
    return df

def get_memory_usage():
    """Get current memory usage in GB"""
    process = psutil.Process()
    mem_gb = process.memory_info().rss / (1024 * 1024 * 1024)
    return f"{mem_gb:.2f} GB"

def process_timeframe(timeframe, chunk_size=10000, sequence_length=60):
    """Process and save one timeframe completely with extreme memory optimization"""
    try:
        print(f"\nProcessing {timeframe} timeframe...")
        print(f"Initial memory usage: {get_memory_usage()}")
        file_path = DATA_DIR / f'v75s_{timeframe}_historical.csv'
        
        # First pass: count total rows and validate data
        print("First pass: counting rows and validating data...")
        total_rows = 0
        for chunk in pd.read_csv(file_path, chunksize=chunk_size):
            total_rows += len(chunk)
        
        # Calculate total sequences possible
        total_sequences = total_rows - sequence_length
        print(f"Total sequences possible: {total_sequences}")
        
        # Initialize memory-mapped arrays immediately
        X_file = SAVE_DIR / f'X_{timeframe}.npy'
        y_file = SAVE_DIR / f'y_{timeframe}.npy'
        
        features = ['open', 'high', 'low', 'close', 'sma_20', 'sma_50', 'ema_20',
                   'macd', 'macd_signal', 'rsi', 'stoch_rsi_k', 'bb_high', 'bb_low',
                   'atr', 'volatility', 'trend_strength', 'body', 'total_range']
        
        X_shape = (total_sequences, sequence_length, len(features))
        print(f"Creating sequence array of shape {X_shape}")
        print(f"Memory usage before creating memmap: {get_memory_usage()}")
        
        # Create memory-mapped array for X
        X = np.memmap(X_file, dtype='float32', mode='w+', shape=X_shape)
        y_data = []
        
        # Process in streaming fashion
        window_buffer = []
        sequence_idx = 0
        chunk_start_idx = 0
        
        print("\nSecond pass: Processing data in streaming fashion...")
        
        # Initialize scaler with first chunk to get feature statistics
        print("Initializing scaler with first chunk...")
        first_chunk = pd.read_csv(file_path, nrows=chunk_size)
        first_chunk['timestamp'] = pd.to_datetime(first_chunk['epoch'], unit='s')
        first_chunk.set_index('timestamp', inplace=True)
        first_chunk = add_technical_indicators(first_chunk)
        first_chunk = calculate_market_regime(first_chunk)
        first_chunk = calculate_candle_patterns(first_chunk)
        
        scaler = StandardScaler()
        scaler.fit(first_chunk[features])
        del first_chunk
        gc.collect()
        
        reader = pd.read_csv(file_path, chunksize=chunk_size)
        for chunk_num, chunk in enumerate(reader, 1):
            print(f"\nProcessing chunk {chunk_num}...")
            print(f"Memory usage: {get_memory_usage()}")
            
            try:
                # Basic preprocessing
                chunk['timestamp'] = pd.to_datetime(chunk['epoch'], unit='s')
                chunk.set_index('timestamp', inplace=True)
                
                # Add features
                chunk = add_technical_indicators(chunk)
                chunk = calculate_market_regime(chunk)
                chunk = calculate_candle_patterns(chunk)
                chunk = create_targets(chunk)
                
                # Scale features
                chunk_scaled = pd.DataFrame(
                    scaler.transform(chunk[features]),
                    columns=features,
                    index=chunk.index
                )
                
                # Update window buffer
                window_buffer.extend(chunk_scaled.values)
                
                # Once we have enough data, start creating sequences
                while len(window_buffer) >= sequence_length:
                    # Create sequence
                    if sequence_idx < total_sequences:
                        X[sequence_idx] = window_buffer[:sequence_length]
                        
                        # Get target data from original chunk
                        target_idx = chunk_start_idx + sequence_length
                        if target_idx < len(chunk):
                            y_data.append({
                                'direction': chunk.iloc[target_idx][[f'{style}_direction' for style in ['scalp', 'day', 'swing', 'position']]].values,
                                'pips': chunk.iloc[target_idx][[f'{style}_pips' for style in ['scalp', 'day', 'swing', 'position']]].values,
                                'tp': chunk.iloc[target_idx][[f'{style}_tp' for style in ['scalp', 'day', 'swing', 'position']]].values,
                                'sl': chunk.iloc[target_idx][[f'{style}_sl' for style in ['scalp', 'day', 'swing', 'position']]].values,
                                'valid': chunk.iloc[target_idx][[f'{style}_valid' for style in ['scalp', 'day', 'swing', 'position']]].values
                            })
                        
                        sequence_idx += 1
                    
                    # Remove oldest data point
                    window_buffer.pop(0)
                    chunk_start_idx += 1
                
                # Periodic cleanup
                if chunk_num % 5 == 0:
                    print("Performing memory cleanup...")
                    gc.collect()
                    
            except Exception as e:
                print(f"Error processing chunk {chunk_num}: {str(e)}")
                continue
        
        # Save targets
        print(f"\nSaving {timeframe} data...")
        print(f"Memory usage before saving: {get_memory_usage()}")
        np.save(y_file, y_data)
        
        # Cleanup
        X.flush()
        del X
        del window_buffer
        del y_data
        gc.collect()
        
        print(f"Completed processing {timeframe}")
        print(f"Final memory usage: {get_memory_usage()}")
        
    except Exception as e:
        print(f"Error processing {timeframe}: {str(e)}")
        import traceback
        traceback.print_exc()

# Define and sort timeframes from largest to smallest
timeframes = ['1m', '5m', '30m', '1h', '8h']
timeframes.sort(reverse=True)  # Now processes: 8h, 1h, 30m, 5m, 1m

print("\nStarting processing with initial memory usage:", get_memory_usage())

# Process each timeframe
for tf in timeframes:
    process_timeframe(tf)
    print("\nForcing complete garbage collection...")
    for _ in range(3):  # Multiple GC passes
        gc.collect()
    print(f"Memory usage after {tf}: {get_memory_usage()}")

print("\nAll preprocessing complete. Data saved to:", SAVE_DIR)

# Cell 4: Advanced Data Preparation and Filtering

# Install ta library (Python Technical Analysis library)
!pip install ta

# First, mount Google Drive
from google.colab import drive
drive.mount('/content/drive')

import os
import numpy as np
import pandas as pd
from sklearn.preprocessing import RobustScaler
from scipy import stats
import ta
from tqdm import tqdm
from pathlib import Path

# Define directory paths
BASE_DIR = Path('/content/drive/MyDrive/V75s_Trading')
DATA_DIR = BASE_DIR / 'historical_data'
SAVE_DIR = BASE_DIR / 'preprocessed_data'

# Create directories if they don't exist
os.makedirs(SAVE_DIR, exist_ok=True)

def calculate_technical_indicators(df):
    """Calculate technical indicators without volume dependency"""
    # Price data
    high = df['High']
    low = df['Low']
    close = df['Close']
    open_price = df['Open']
    
    # Basic price indicators
    df['Returns'] = close.pct_change()
    df['Range'] = high - low
    df['HL2'] = (high + low) / 2
    df['HLC3'] = (high + low + close) / 3
    df['OHLC4'] = (open_price + high + low + close) / 4
    
    # Trend Indicators
    df['SMA_10'] = ta.trend.sma_indicator(close, window=10)
    df['SMA_20'] = ta.trend.sma_indicator(close, window=20)
    df['SMA_50'] = ta.trend.sma_indicator(close, window=50)
    df['EMA_10'] = ta.trend.ema_indicator(close, window=10)
    df['EMA_20'] = ta.trend.ema_indicator(close, window=20)
    df['EMA_50'] = ta.trend.ema_indicator(close, window=50)
    df['MACD'] = ta.trend.macd_diff(close)
    df['ADX'] = ta.trend.adx(high, low, close)
    
    # Momentum Indicators
    df['RSI'] = ta.momentum.rsi(close)
    df['STOCH_K'] = ta.momentum.stoch(high, low, close)
    df['STOCH_D'] = ta.momentum.stoch_signal(high, low, close)
    df['ROC'] = ta.momentum.roc(close)
    df['PPO'] = ta.momentum.ppo_signal(close)
    
    # Volatility Indicators
    df['BB_UPPER'] = ta.volatility.bollinger_hband(close)
    df['BB_MIDDLE'] = ta.volatility.bollinger_mavg(close)
    df['BB_LOWER'] = ta.volatility.bollinger_lband(close)
    df['ATR'] = ta.volatility.average_true_range(high, low, close)
    df['KC_UPPER'] = ta.volatility.keltner_channel_hband(high, low, close)
    df['KC_LOWER'] = ta.volatility.keltner_channel_lband(high, low, close)
    
    # Remove any NaN values using newer pandas syntax
    return df.ffill().bfill()

def process_csv_file(file_path):
    """Process a single CSV file and return preprocessed data"""
    print(f"\nProcessing {file_path.name}...")
    
    # Read CSV file
    df = pd.read_csv(file_path)
    print(f"Original columns: {df.columns.tolist()}")
    
    # Map column names
    column_mapping = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
    }
    
    # Rename columns to match expected format
    df = df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns})
    print(f"Mapped columns: {df.columns.tolist()}")
    
    # Ensure required columns exist
    required_columns = ['Open', 'High', 'Low', 'Close']
    if not all(col in df.columns for col in required_columns):
        missing_cols = [col for col in required_columns if col not in df.columns]
        print(f"Missing required columns: {missing_cols}")
        return None, None
    
    # Calculate technical indicators
    df_processed = calculate_technical_indicators(df)
    
    # Select features for X (all numeric columns except datetime and epoch)
    feature_cols = df_processed.select_dtypes(include=[np.number]).columns
    X = df_processed[feature_cols].values
    
    # Create targets (multi-target approach)
    future_returns = df_processed['Close'].pct_change().shift(-1)
    y = np.column_stack([
        (future_returns > 0).astype(int),  # Direction
        (future_returns > 0.001).astype(int),  # Significant up move
        (future_returns < -0.001).astype(int)  # Significant down move
    ])[:-1]  # Remove last row since we don't have future data for it
    
    return X[:-1], y  # Remove last row to match target shape

# Process each timeframe
timeframes = {
    '1m': 'v75s_1m_historical.csv',
    '5m': 'v75s_5m_historical.csv',
    '15m': 'v75s_15m_historical.csv',
    '1h': 'v75s_1h_historical.csv',
    '4h': 'v75s_4h_historical.csv',
    '24h': 'v75s_24h_historical.csv'
}

print("\nStarting data preprocessing from raw CSV files...")

for tf, filename in timeframes.items():
    csv_path = DATA_DIR / filename
    if not csv_path.exists():
        print(f"\nSkipping {tf} - CSV file not found")
        continue
        
    try:
        # Process CSV file
        X, y = process_csv_file(csv_path)
        
        if X is None or y is None:
            print(f"Skipping {tf} - Processing failed")
            continue
            
        # Save preprocessed data
        x_path = SAVE_DIR / f'X_{tf}.npy'
        y_path = SAVE_DIR / f'y_{tf}.npy'
        
        np.save(str(x_path), X)
        np.save(str(y_path), y)
        
        print(f"Saved preprocessed data for {tf}")
        print(f"X shape: {X.shape}, y shape: {y.shape}")
        print(f"Number of features: {X.shape[1]}")
        
    except Exception as e:
        print(f"Error processing {tf}: {str(e)}")
        continue

print("\nPreprocessing complete!")

# Verify the newly saved files
print("\nVerifying saved files:")
for tf in timeframes:
    x_path = SAVE_DIR / f'X_{tf}.npy'
    y_path = SAVE_DIR / f'y_{tf}.npy'
    if x_path.exists() and y_path.exists():
        try:
            X = np.load(str(x_path), allow_pickle=False)
            y = np.load(str(y_path), allow_pickle=False)
            print(f"\n{tf} timeframe:")
            print(f"X shape: {X.shape}")
            print(f"y shape: {y.shape}")
        except Exception as e:
            print(f"Error loading {tf} data: {str(e)}")

# Cell 5: Optimized GPU Training for Hybrid Model

# First, verify Colab runtime and GPU
try:
    from google.colab import drive
    import torch

    # Check if GPU is available
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name()
        print(f"GPU is available: {device_name}")
        # Set default tensor type to cuda
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
    else:
        print("No GPU found. Running on CPU")
    
    # Mount Google Drive
    drive.mount('/content/drive')
    print("Google Drive mounted successfully")
    
except ImportError as e:
    print("Error: This notebook should be run in Google Colab")
    raise e

print("Starting execution with verified runtime...")

# [Rest of the imports and code remains the same]
import torch
from torch.utils.data import Dataset, DataLoader, random_split
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import math
from torch.nn.utils.rnn import pad_sequence
import logging
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
import numpy.typing as npt
from sklearn.preprocessing import StandardScaler, RobustScaler
from tqdm.notebook import tqdm
import warnings
warnings.filterwarnings('ignore')

print("Imports completed")

# Optimized training constants
SEQUENCE_LENGTH = 60
FEATURE_DIM = 29
EMBEDDING_DIM = 256
BATCH_SIZE = 64
NUM_EPOCHS = 100
LEARNING_RATE = 0.001
ACCURACY_THRESHOLD = 0.15
VALIDATION_SPLIT = 0.2
GRADIENT_CLIP = 1.0
MODEL_SAVE_DIR = Path('/content/drive/MyDrive/V75s_Trading/models')

# Create model save directory
MODEL_SAVE_DIR.mkdir(parents=True, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    force=True
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

if not logger.handlers:
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

print("Environment setup completed successfully")

# Optimized training constants
SEQUENCE_LENGTH = 60
FEATURE_DIM = 29
EMBEDDING_DIM = 256
BATCH_SIZE = 64  # Increased batch size for better stability
NUM_EPOCHS = 100  # Adjusted number of epochs
LEARNING_RATE = 0.001  # Increased initial learning rate
ACCURACY_THRESHOLD = 0.15  # Reduced threshold for more realistic accuracy calculation
VALIDATION_SPLIT = 0.2
GRADIENT_CLIP = 1.0  # Increased gradient clip threshold

@dataclass
class BatchData:
    """Data class to hold batch information"""
    features: torch.Tensor
    targets: torch.Tensor

class TimeSeriesDataset(Dataset):
    """Dataset class for V75s time series data with fixed-length sequences"""
    
    def __init__(self, X: Union[np.ndarray, torch.Tensor], 
                 y: Union[np.ndarray, torch.Tensor], 
                 seq_length: int = SEQUENCE_LENGTH,
                 scaler: Optional[RobustScaler] = None):
        """
        Initialize dataset with robust scaling and improved preprocessing
        
        Args:
            X: Feature data of shape [N, feature_dim] or [N, seq_len, feature_dim]
            y: Target data of shape [N, num_targets]
            seq_length: Desired length for all sequences
        """
        # Convert to numpy for preprocessing
        X = X.numpy() if isinstance(X, torch.Tensor) else X
        y = y.numpy() if isinstance(y, torch.Tensor) else y
        
        # Reshape if needed
        if X.ndim == 2:
            X = X.reshape(-1, X.shape[-1])
        
        # Initialize scaler with improved parameters
        if scaler is None:
            self.scaler = RobustScaler(
                with_centering=True,
                with_scaling=True,
                quantile_range=(1.0, 99.0)  # More robust to outliers
            )
        else:
            self.scaler = scaler
            
        # Scale features with extra safety checks
        X_clean = np.nan_to_num(X, nan=0.0, posinf=1e6, neginf=-1e6)
        X_scaled = self.scaler.fit_transform(X_clean)
        
        # Clip extreme values
        X_scaled = np.clip(X_scaled, -10, 10)
        
        # Scale targets separately for each column
        self.target_scalers = []
        y_scaled = np.zeros_like(y, dtype=np.float32)
        
        for i in range(y.shape[1]):
            scaler = RobustScaler(
                with_centering=True,
                with_scaling=True,
                quantile_range=(1.0, 99.0)
            )
            y_scaled[:, i] = scaler.fit_transform(y[:, i].reshape(-1, 1)).ravel()
            self.target_scalers.append(scaler)
        
        # Convert to tensors
        self.X = torch.FloatTensor(X_scaled)
        self.y = torch.FloatTensor(y_scaled)
        
        # Create sequences
        self.X = self.create_sequences(self.X, seq_length)
        self.seq_length = seq_length
        
        logger.info(f"Dataset initialized - X shape: {self.X.shape}, y shape: {self.y.shape}")
        
    def create_sequences(self, data: torch.Tensor, seq_length: int) -> torch.Tensor:
        """Create sequences for temporal modeling"""
        sequences = []
        for i in range(len(data) - seq_length + 1):
            sequence = data[i:i + seq_length]
            sequences.append(sequence)
        return torch.stack(sequences)
    
    def __len__(self) -> int:
        return len(self.X)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return self.X[idx], self.y[idx]
    
    def denormalize_targets(self, y: torch.Tensor) -> torch.Tensor:
        """Denormalize predictions back to original scale"""
        y_np = y.cpu().numpy()
        y_denorm = np.zeros_like(y_np)
        
        for i in range(y_np.shape[1]):
            y_denorm[:, i] = self.target_scalers[i].inverse_transform(
                y_np[:, i].reshape(-1, 1)
            ).ravel()
            
        return torch.FloatTensor(y_denorm).to(y.device)

def collate_fn(batch: List[Tuple[torch.Tensor, torch.Tensor]]) -> BatchData:
    """
    Custom collate function to handle batches of sequences.
    
    Args:
        batch: List of tuples (features, targets)
        
    Returns:
        BatchData containing batched features and targets
    """
    try:
        # Separate features and labels
        sequences, labels = zip(*batch)
        
        # Stack into batches
        features = torch.stack(sequences)  # Shape: [batch_size, sequence_length, feature_dim]
        labels = torch.stack(labels)       # Shape: [batch_size, num_targets]
        
        return BatchData(features=features, targets=labels)
    except Exception as e:
        logger.error(f"Error in collate_fn: {e}")
        logger.error(f"Batch contents: {batch}")
        raise

class V75sHybridModel(nn.Module):
    """Hybrid model architecture"""
    def __init__(self, input_dim: int, hidden_dim: int = EMBEDDING_DIM, 
                 num_layers: int = 2, num_heads: int = 4, 
                 dropout: float = 0.2, num_targets: int = 3):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # Input processing with CNN embedding
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Multi-scale CNN blocks
        self.cnn_blocks = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(hidden_dim, hidden_dim, kernel_size=k, padding=k//2),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Conv1d(hidden_dim, hidden_dim, kernel_size=1),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for k in [3, 5, 7]
        ])
        
        # BiLSTM
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        self.lstm_proj = nn.Linear(hidden_dim * 2, hidden_dim)
        
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        fusion_size = hidden_dim * 4
        
        self.fusion = nn.Sequential(
            nn.Linear(fusion_size, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_targets)
        )
        
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Conv1d)):
            nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
        elif isinstance(module, (nn.BatchNorm1d, nn.LayerNorm)):
            nn.init.constant_(module.weight, 1)
            nn.init.constant_(module.bias, 0)
            
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len = x.size(0), x.size(1)
        
        x = self.input_projection(x)
        
        x_cnn = x.transpose(1, 2)
        cnn_outputs = []
        for cnn in self.cnn_blocks:
            cnn_out = cnn(x_cnn)
            cnn_outputs.append(cnn_out)
        
        x_cnn = torch.stack(cnn_outputs).mean(0)
        x_cnn = x_cnn.transpose(1, 2)
        
        lstm_out, (h_n, _) = self.lstm(x_cnn)
        lstm_out = self.lstm_proj(lstm_out)
        
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        attn_out = attn_out + lstm_out
        
        last_lstm = lstm_out[:, -1]
        last_attn = attn_out[:, -1]
        max_pool = torch.max(x_cnn, dim=1)[0]
        avg_pool = torch.mean(x_cnn, dim=1)
        
        fusion_input = torch.cat([
            last_lstm, last_attn, max_pool, avg_pool
        ], dim=1)
        
        out = self.fusion(fusion_input)
        
        return out

def calculate_metrics(outputs: torch.Tensor, targets: torch.Tensor, 
                     dataset: TimeSeriesDataset,
                     threshold: float = ACCURACY_THRESHOLD) -> Tuple[float, Dict[str, float]]:
    with torch.no_grad():
        loss = F.mse_loss(outputs, targets)
        
        correct = torch.abs(outputs - targets) <= threshold
        accuracies = correct.float().mean(dim=0)
        
        denorm_outputs = dataset.denormalize_targets(outputs)
        denorm_targets = dataset.denormalize_targets(targets)
        
        denorm_correct = torch.abs(denorm_outputs - denorm_targets) <= threshold
        denorm_accuracies = denorm_correct.float().mean(dim=0)
        
        metrics = {
            'loss': loss.item(),
            'avg_accuracy': correct.float().mean().item() * 100,
            'target1_acc': accuracies[0].item() * 100,
            'target2_acc': accuracies[1].item() * 100,
            'target3_acc': accuracies[2].item() * 100,
            'denorm_avg_acc': denorm_correct.float().mean().item() * 100,
        }
        
        return loss, metrics

def train_model():
    """Enhanced training function with improved stability and checkpointing"""
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {device}")
        
        model = V75sHybridModel(
            input_dim=FEATURE_DIM,
            hidden_dim=EMBEDDING_DIM,
            num_layers=2,
            num_heads=4,
            dropout=0.2,
            num_targets=3
        ).to(device)
        
        logger.info("Model initialized successfully")
        
        base_path = Path('/content/drive/MyDrive/V75s_Trading/preprocessed_data')
        logger.info(f"Loading data from: {base_path}")
        
        if not base_path.exists():
            raise ValueError(f"Data directory not found at {base_path}")
        
        x_path = base_path / 'X_1m.npy'
        y_path = base_path / 'y_1m.npy'
        
        if not x_path.exists() or not y_path.exists():
            raise FileNotFoundError(f"Data files not found at {x_path} or {y_path}")
        
        X = np.load(x_path)
        y = np.load(y_path)
        
        logger.info(f"Data loaded - X shape: {X.shape}, y shape: {y.shape}")
        
        dataset = TimeSeriesDataset(X, y)
        train_size = int((1 - VALIDATION_SPLIT) * len(dataset))
        val_size = len(dataset) - train_size
        
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=BATCH_SIZE,
            shuffle=True,
            num_workers=2,  # Increased workers
            pin_memory=True,
            collate_fn=collate_fn
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=BATCH_SIZE,
            shuffle=False,
            num_workers=2,
            pin_memory=True,
            collate_fn=collate_fn
        )
        
        # Improved optimizer setup with weight decay
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=LEARNING_RATE,
            weight_decay=0.01,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # Better learning rate scheduling
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=LEARNING_RATE,
            epochs=NUM_EPOCHS,
            steps_per_epoch=len(train_loader),
            pct_start=0.2,  # Faster warmup
            anneal_strategy='cos',
            final_div_factor=1e3
        )
        
        criterion = nn.MSELoss()
        
        # Training state tracking
        best_val_loss = float('inf')
        best_accuracy = 0.0
        patience = 20
        patience_counter = 0
        min_epochs = 30
        
        # Save the initial model state
        save_checkpoint(model, optimizer, scheduler, 0, float('inf'), 
                       {'avg_accuracy': 0}, MODEL_SAVE_DIR / 'initial_model.pt')
        
        logger.info(f"\nStarting training for {NUM_EPOCHS} epochs")
        
        for epoch in range(NUM_EPOCHS):
            model.train()
            epoch_loss = 0
            epoch_metrics = {
                'avg_accuracy': 0,
                'target1_acc': 0,
                'target2_acc': 0,
                'target3_acc': 0
            }
            
            progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{NUM_EPOCHS}")
            num_batches = 0
            
            for batch in progress_bar:
                features = batch.features.to(device)
                targets = batch.targets.to(device)
                
                optimizer.zero_grad()
                outputs = model(features)
                loss, batch_metrics = calculate_metrics(outputs, targets, dataset)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), GRADIENT_CLIP)
                optimizer.step()
                scheduler.step()
                
                epoch_loss += loss.item()
                for key in epoch_metrics:
                    epoch_metrics[key] += batch_metrics[key]
                num_batches += 1
                
                # Update progress bar with moving averages
                progress_bar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'acc': f"{batch_metrics['avg_accuracy']:.2f}%"
                })
            
            # Calculate epoch averages
            epoch_loss /= num_batches
            for key in epoch_metrics:
                epoch_metrics[key] /= num_batches
            
            # Validation phase
            model.eval()
            val_loss = 0
            val_metrics = {
                'avg_accuracy': 0,
                'target1_acc': 0,
                'target2_acc': 0,
                'target3_acc': 0
            }
            num_val_batches = 0
            
            with torch.no_grad():
                for batch in val_loader:
                    features = batch.features.to(device)
                    targets = batch.targets.to(device)
                    
                    outputs = model(features)
                    loss, batch_metrics = calculate_metrics(outputs, targets, dataset)
                    
                    val_loss += loss.item()
                    for key in val_metrics:
                        val_metrics[key] += batch_metrics[key]
                    num_val_batches += 1
            
            val_loss /= num_val_batches
            for key in val_metrics:
                val_metrics[key] /= num_val_batches
            
            # Detailed epoch summary
            logger.info(
                f"\nEpoch {epoch+1}/{NUM_EPOCHS}:"
                f"\nTrain - Loss: {epoch_loss:.4f}, Accuracy: {epoch_metrics['avg_accuracy']:.2f}%"
                f"\nTarget Accuracies - T1: {epoch_metrics['target1_acc']:.2f}%, "
                f"T2: {epoch_metrics['target2_acc']:.2f}%, "
                f"T3: {epoch_metrics['target3_acc']:.2f}%"
                f"\nVal - Loss: {val_loss:.4f}, Accuracy: {val_metrics['avg_accuracy']:.2f}%"
            )
            
            # Save model on multiple conditions
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                save_checkpoint(model, optimizer, scheduler, epoch, val_loss, 
                              val_metrics, MODEL_SAVE_DIR / 'best_loss_model.pt')
            
            if val_metrics['avg_accuracy'] > best_accuracy:
                best_accuracy = val_metrics['avg_accuracy']
                save_checkpoint(model, optimizer, scheduler, epoch, val_loss,
                              val_metrics, MODEL_SAVE_DIR / 'best_accuracy_model.pt')
                
                # Save additional milestone models
                if best_accuracy >= 90 and (int(best_accuracy) % 1 == 0):  # Save at each integer milestone >=90
                    save_checkpoint(model, optimizer, scheduler, epoch, val_loss,
                                  val_metrics, MODEL_SAVE_DIR / f'model_acc_{int(best_accuracy)}.pt')
            
            # Save periodic checkpoints every 10 epochs
            if (epoch + 1) % 10 == 0:
                save_checkpoint(model, optimizer, scheduler, epoch, val_loss,
                              val_metrics, MODEL_SAVE_DIR / f'checkpoint_epoch_{epoch+1}.pt')
        
        return model, {
            'final_train_loss': epoch_loss,
            'final_train_acc': epoch_metrics['avg_accuracy'],
            'final_val_loss': val_loss,
            'final_val_acc': val_metrics['avg_accuracy']
        }
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        logger.exception("Detailed error trace:")
        raise

def load_saved_model(model_path: Union[str, Path], device: torch.device = None) -> V75sHybridModel:
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    checkpoint = torch.load(model_path, map_location=device)
    model = V75sHybridModel(
        input_dim=FEATURE_DIM,
        hidden_dim=EMBEDDING_DIM,
        num_layers=2,
        num_heads=4,
        dropout=0.2,
        num_targets=3
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    return model