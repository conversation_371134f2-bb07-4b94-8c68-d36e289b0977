//+------------------------------------------------------------------+
//|                                           403Spike8Detector.mq5 |
//|                                    Professional V75 Spike Trader |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "V75 Spike Trading EA"
#property version   "1.00"
#property description "Professional EA for trading Volatility 75 Index spikes"

//--- Input Parameters
input group "=== SPIKE DETECTION ==="
input double   MinSpikeSize = 50.0;          // Minimum spike size in points
input double   MinBodyRatio = 0.3;           // Minimum body ratio (0.0-1.0)
input double   MinATRMultiplier = 1.5;       // Minimum ATR multiplier

input group "=== RISK MANAGEMENT ==="
input double   FixedLot = 0.1;               // Fixed lot size
input double   StopLoss = 20.0;              // Stop loss in points
input double   TakeProfit = 150.0;           // Take profit in points
input int      MaxTradeDuration = 300;       // Max trade duration in seconds
input double   EmergencyProfitLevel = 100.0; // Emergency profit close level
input double   EmergencyLossLevel = 50.0;    // Emergency loss close level

input group "=== TRADE SETTINGS ==="
input bool     UseContrarian = false;        // Trade against spikes
input int      CooldownSeconds = 30;         // Cooldown between trades
input int      MagicNumber = 12345;          // Magic number for identification
input string   TradeComment = "V75_SPIKE";   // Trade comment prefix

//--- Global Variables
int            atr_handle;
double         atr_buffer[];
datetime       last_bar_time = 0;
datetime       last_trade_time = 0;
int            total_trades = 0;
int            winning_trades = 0;
double         total_profit = 0.0;

//--- Trade structures
MqlTradeRequest request;
MqlTradeResult result;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize ATR indicator
    atr_handle = iATR(_Symbol, PERIOD_M1, 14);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("ERROR: Failed to create ATR indicator handle");
        return INIT_FAILED;
    }
    
    // Set array as series
    ArraySetAsSeries(atr_buffer, true);
    
    // Initialize trade structures
    ZeroMemory(request);
    ZeroMemory(result);
    
    // Validate symbol and timeframe
    if(_Symbol != "Volatility 75 Index" && StringFind(_Symbol, "V75") == -1)
    {
        Print("WARNING: This EA is designed for Volatility 75 Index");
    }
    
    if(_Period != PERIOD_M1)
    {
        Print("WARNING: This EA is optimized for M1 timeframe");
    }
    
    Print("=== 403Spike8Detector EA Initialized ===");
    Print("Symbol: ", _Symbol);
    Print("Timeframe: ", EnumToString(_Period));
    Print("Min Spike Size: ", MinSpikeSize, " points");
    Print("Strategy: ", UseContrarian ? "Contrarian" : "Trend Following");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    
    // Print final statistics
    PrintStatistics();
    
    Print("=== 403Spike8Detector EA Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for new bar
    if(!IsNewBar())
        return;
    
    // Check existing positions first
    CheckPositions();
    
    // Check cooldown period
    if(TimeCurrent() - last_trade_time < CooldownSeconds)
        return;
    
    // Look for new trade opportunities only when no positions exist
    if(PositionsTotal() == 0)
    {
        CheckForSpike();
    }
}

//+------------------------------------------------------------------+
//| Check for new bar formation                                     |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime current_bar_time = iTime(_Symbol, PERIOD_M1, 0);
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check for spike patterns                                        |
//+------------------------------------------------------------------+
void CheckForSpike()
{
    // Get previous bar data (completed candle)
    double high = iHigh(_Symbol, PERIOD_M1, 1);
    double low = iLow(_Symbol, PERIOD_M1, 1);
    double open = iOpen(_Symbol, PERIOD_M1, 1);
    double close = iClose(_Symbol, PERIOD_M1, 1);
    
    // Validate data
    if(high == 0 || low == 0 || open == 0 || close == 0)
    {
        Print("ERROR: Invalid price data");
        return;
    }
    
    // Get ATR data
    if(CopyBuffer(atr_handle, 0, 1, 1, atr_buffer) <= 0)
    {
        Print("ERROR: Failed to get ATR data");
        return;
    }
    
    double atr_value = atr_buffer[0];
    if(atr_value <= 0)
    {
        Print("ERROR: Invalid ATR value");
        return;
    }
    
    // Calculate spike metrics
    double spike_range = (high - low) / _Point;
    double body_size = MathAbs(close - open) / _Point;
    double body_ratio = body_size / spike_range;
    double atr_points = atr_value / _Point;
    double atr_multiplier = spike_range / atr_points;
    
    // Apply spike validation criteria
    if(spike_range < MinSpikeSize)
        return;
    
    if(body_ratio < MinBodyRatio)
        return;
    
    if(atr_multiplier < MinATRMultiplier)
        return;
    
    // Determine trade direction
    bool is_bullish_spike = (close > open);
    ENUM_ORDER_TYPE order_type;
    
    if(UseContrarian)
    {
        // Contrarian strategy - trade against the spike
        order_type = is_bullish_spike ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    }
    else
    {
        // Trend following strategy - trade with the spike
        order_type = is_bullish_spike ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    }
    
    // Log spike detection
    Print("SPIKE DETECTED:");
    Print("  Range: ", DoubleToString(spike_range, 1), " points");
    Print("  Body Ratio: ", DoubleToString(body_ratio, 3));
    Print("  ATR Multiplier: ", DoubleToString(atr_multiplier, 2));
    Print("  Direction: ", is_bullish_spike ? "Bullish" : "Bearish");
    Print("  Trade Type: ", EnumToString(order_type));
    
    // Execute trade
    ExecuteTrade(order_type);
}

//+------------------------------------------------------------------+
//| Execute trade based on spike detection                          |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE order_type)
{
    // Double-check that we don't have existing positions
    if(PositionsTotal() > 0)
    {
        Print("WARNING: Existing position found, skipping new trade");
        return;
    }

    // Get current market prices
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    if(ask <= 0 || bid <= 0)
    {
        Print("ERROR: Invalid market prices. Ask: ", ask, " Bid: ", bid);
        return;
    }

    // Prepare trade request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = FixedLot;
    request.type = order_type;
    request.price = (order_type == ORDER_TYPE_BUY) ? ask : bid;
    request.deviation = 5;  // Increased deviation for V75
    request.magic = MagicNumber;
    request.comment = TradeComment + "_" + TimeToString(TimeCurrent(), TIME_SECONDS);
    request.type_filling = ORDER_FILLING_FOK;  // Fill or Kill

    Print("Preparing trade:");
    Print("  Type: ", EnumToString(order_type));
    Print("  Volume: ", request.volume);
    Print("  Price: ", DoubleToString(request.price, _Digits));
    Print("  Ask: ", DoubleToString(ask, _Digits));
    Print("  Bid: ", DoubleToString(bid, _Digits));

    // Validate lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    if(request.volume < min_lot || request.volume > max_lot)
    {
        Print("ERROR: Invalid lot size. Min: ", min_lot, " Max: ", max_lot, " Current: ", request.volume);
        return;
    }

    // Check margin requirements
    double margin_required;
    if(!OrderCalcMargin(order_type, _Symbol, request.volume, request.price, margin_required))
    {
        Print("ERROR: Failed to calculate margin requirements");
        return;
    }

    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    if(margin_required > free_margin)
    {
        Print("ERROR: Insufficient margin. Required: ", margin_required, " Available: ", free_margin);
        return;
    }

    Print("Margin check passed. Required: ", margin_required, " Available: ", free_margin);

    // Execute trade
    if(!OrderSend(request, result))
    {
        Print("ERROR: Trade execution failed. Error: ", GetLastError());
        Print("  Retcode: ", result.retcode);
        Print("  Deal: ", result.deal);
        Print("  Order: ", result.order);
        return;
    }

    // Check execution result
    if(result.retcode == TRADE_RETCODE_DONE)
    {
        Print("TRADE EXECUTED SUCCESSFULLY:");
        Print("  Order: ", result.order);
        Print("  Deal: ", result.deal);
        Print("  Volume: ", result.volume);
        Print("  Price: ", DoubleToString(result.price, _Digits));

        // Set SL/TP after successful execution
        if(result.deal > 0)
        {
            SetSLTP(result.deal, order_type);
        }

        // Update statistics
        total_trades++;
        last_trade_time = TimeCurrent();
    }
    else
    {
        Print("ERROR: Trade execution failed with retcode: ", result.retcode);
        Print("  Available retcodes info:");
        Print("  TRADE_RETCODE_DONE = ", TRADE_RETCODE_DONE);
        Print("  TRADE_RETCODE_PLACED = ", TRADE_RETCODE_PLACED);
        Print("  Current retcode = ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Set Stop Loss and Take Profit                                   |
//+------------------------------------------------------------------+
void SetSLTP(ulong deal_ticket, ENUM_ORDER_TYPE original_type)
{
    // Wait a moment for position to be fully opened
    Sleep(100);

    // Find the opened position by symbol and magic number
    if(!PositionSelect(_Symbol))
    {
        Print("ERROR: Cannot find opened position for SL/TP setting");
        return;
    }

    // Verify this is our position
    if(PositionGetInteger(POSITION_MAGIC) != MagicNumber)
    {
        Print("ERROR: Position magic number mismatch");
        return;
    }

    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    ulong position_ticket = PositionGetInteger(POSITION_TICKET);

    Print("Setting SL/TP for position:");
    Print("  Ticket: ", position_ticket);
    Print("  Type: ", EnumToString(position_type));
    Print("  Open Price: ", DoubleToString(position_open_price, _Digits));

    // Get current market prices
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double current_price = (position_type == POSITION_TYPE_BUY) ? bid : ask;

    // Calculate SL and TP levels with smaller, more reasonable distances
    double sl_level, tp_level;
    double sl_distance = StopLoss * _Point;
    double tp_distance = TakeProfit * _Point;

    // Reduce distances if they're too large for V75
    if(StopLoss > 10.0) sl_distance = 10.0 * _Point;  // Max 10 points SL
    if(TakeProfit > 50.0) tp_distance = 50.0 * _Point; // Max 50 points TP

    if(position_type == POSITION_TYPE_BUY)
    {
        sl_level = position_open_price - sl_distance;
        tp_level = position_open_price + tp_distance;
    }
    else
    {
        sl_level = position_open_price + sl_distance;
        tp_level = position_open_price - tp_distance;
    }

    // Get broker requirements
    double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    double freeze_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL) * _Point;

    Print("Broker Requirements:");
    Print("  Min Stop Level: ", DoubleToString(min_stop_level, _Digits));
    Print("  Freeze Level: ", DoubleToString(freeze_level, _Digits));
    Print("  Current Price: ", DoubleToString(current_price, _Digits));

    // Validate and adjust SL/TP levels
    if(position_type == POSITION_TYPE_BUY)
    {
        // For BUY positions
        if(current_price - sl_level < min_stop_level)
        {
            sl_level = current_price - min_stop_level - _Point;
            Print("WARNING: SL adjusted to minimum stop level: ", DoubleToString(sl_level, _Digits));
        }
        if(tp_level - current_price < min_stop_level)
        {
            tp_level = current_price + min_stop_level + _Point;
            Print("WARNING: TP adjusted to minimum stop level: ", DoubleToString(tp_level, _Digits));
        }
    }
    else
    {
        // For SELL positions
        if(sl_level - current_price < min_stop_level)
        {
            sl_level = current_price + min_stop_level + _Point;
            Print("WARNING: SL adjusted to minimum stop level: ", DoubleToString(sl_level, _Digits));
        }
        if(current_price - tp_level < min_stop_level)
        {
            tp_level = current_price - min_stop_level - _Point;
            Print("WARNING: TP adjusted to minimum stop level: ", DoubleToString(tp_level, _Digits));
        }
    }

    // Normalize price levels
    sl_level = NormalizeDouble(sl_level, _Digits);
    tp_level = NormalizeDouble(tp_level, _Digits);

    Print("Calculated SL/TP:");
    Print("  SL Level: ", DoubleToString(sl_level, _Digits));
    Print("  TP Level: ", DoubleToString(tp_level, _Digits));

    // Prepare modification request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_SLTP;
    request.symbol = _Symbol;
    request.position = position_ticket;  // Specify position ticket
    request.sl = sl_level;
    request.tp = tp_level;
    request.magic = MagicNumber;

    // Execute modification
    if(!OrderSend(request, result))
    {
        Print("ERROR: Failed to set SL/TP. Error: ", GetLastError());
        Print("  Request details:");
        Print("    Action: ", EnumToString(request.action));
        Print("    Symbol: ", request.symbol);
        Print("    Position: ", request.position);
        Print("    SL: ", DoubleToString(request.sl, _Digits));
        Print("    TP: ", DoubleToString(request.tp, _Digits));
        return;
    }

    if(result.retcode == TRADE_RETCODE_DONE)
    {
        Print("SL/TP SET SUCCESSFULLY:");
        Print("  SL: ", DoubleToString(sl_level, _Digits));
        Print("  TP: ", DoubleToString(tp_level, _Digits));
    }
    else
    {
        Print("ERROR: SL/TP modification failed with retcode: ", result.retcode);
        Print("  Result details:");
        Print("    Retcode: ", result.retcode);
        Print("    Deal: ", result.deal);
        Print("    Order: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| Check and manage existing positions                             |
//+------------------------------------------------------------------+
void CheckPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i) <= 0)
            continue;

        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber)
            continue;

        if(PositionGetString(POSITION_SYMBOL) != _Symbol)
            continue;

        // Get position details
        double position_profit = PositionGetDouble(POSITION_PROFIT);
        datetime position_time = (datetime)PositionGetInteger(POSITION_TIME);
        ulong position_ticket = PositionGetInteger(POSITION_TICKET);

        bool should_close = false;
        string close_reason = "";

        // Check maximum trade duration
        if(TimeCurrent() - position_time >= MaxTradeDuration)
        {
            should_close = true;
            close_reason = "Max Duration Reached";
        }

        // Check emergency profit level
        if(position_profit >= EmergencyProfitLevel)
        {
            should_close = true;
            close_reason = "Emergency Profit Level";
        }

        // Check emergency loss level
        if(position_profit <= -EmergencyLossLevel)
        {
            should_close = true;
            close_reason = "Emergency Loss Level";
        }

        // Close position if needed
        if(should_close)
        {
            ClosePosition(position_ticket, close_reason);
        }
    }
}

//+------------------------------------------------------------------+
//| Close position by ticket                                        |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket, string reason)
{
    if(!PositionSelectByTicket(ticket))
    {
        Print("ERROR: Cannot select position for closing: ", ticket);
        return;
    }

    double position_volume = PositionGetDouble(POSITION_VOLUME);
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double close_price = (position_type == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Prepare close request
    ZeroMemory(request);
    ZeroMemory(result);

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = position_volume;
    request.type = (position_type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.price = close_price;
    request.deviation = 3;
    request.magic = MagicNumber;
    request.comment = TradeComment + "_CLOSE_" + reason;
    request.position = ticket;

    // Execute close
    if(!OrderSend(request, result))
    {
        Print("ERROR: Failed to close position. Error: ", GetLastError());
        return;
    }

    if(result.retcode == TRADE_RETCODE_DONE)
    {
        double profit = PositionGetDouble(POSITION_PROFIT);
        Print("POSITION CLOSED:");
        Print("  Ticket: ", ticket);
        Print("  Reason: ", reason);
        Print("  Profit: $", DoubleToString(profit, 2));

        // Update statistics
        total_profit += profit;
        if(profit > 0)
            winning_trades++;
    }
    else
    {
        Print("ERROR: Position close failed with retcode: ", result.retcode);
    }
}

//+------------------------------------------------------------------+
//| Print trading statistics                                        |
//+------------------------------------------------------------------+
void PrintStatistics()
{
    if(total_trades == 0)
    {
        Print("=== TRADING STATISTICS ===");
        Print("No trades executed");
        return;
    }

    double win_rate = (double)winning_trades / total_trades * 100.0;
    int losing_trades = total_trades - winning_trades;

    Print("=== TRADING STATISTICS ===");
    Print("Total Trades: ", total_trades);
    Print("Winning Trades: ", winning_trades);
    Print("Losing Trades: ", losing_trades);
    Print("Win Rate: ", DoubleToString(win_rate, 1), "%");
    Print("Total Profit: $", DoubleToString(total_profit, 2));

    if(total_trades > 0)
    {
        double avg_profit = total_profit / total_trades;
        Print("Average Profit per Trade: $", DoubleToString(avg_profit, 2));
    }

    Print("========================");
}
